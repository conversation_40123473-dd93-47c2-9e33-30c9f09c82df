import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../constants/colors.dart';
import '../utils/animation_utils.dart';
import '../widgets/custom_carousel.dart';
import '../widgets/custom_menu_icon.dart';
import '../widgets/navigation_menu_drawer.dart';
import '../widgets/hot_deals_section.dart';
import '../widgets/personalized_recommendations_section.dart';
import '../widgets/global_footer.dart';
import '../services/cart_service.dart';
import '../services/language_service.dart';
import 'order_type_selection_screen.dart';

class HomeScreen extends StatefulWidget {
  final CartService cartService;

  const HomeScreen({Key? key, required this.cartService}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  bool _isButtonHovered = false;

  // Placeholder carousel items
  final List<Map<String, String>> carouselItems = [
    {
      'title': 'EARN POINTS',
      'subtitle': 'BY PLAYING GAMES',
      'color': '#FF6B35',
      'category': 'Loyalty',
    },
    {
      'title': 'Nashville Hot Chicken',
      'subtitle': 'Feel the heat!',
      'color': '#FF5722',
      'category': 'Chicken Pieces',
    },
    {
      'title': 'Crew Pack Special',
      'subtitle': 'Perfect for sharing',
      'color': '#F4511E',
      'category': 'CREW Combos',
    },
    {
      'title': 'Fresh Sides',
      'subtitle': 'Complete your meal',
      'color': '#E64A19',
      'category': 'Sides',
    },
    {
      'title': 'Spicy Tenders',
      'subtitle': 'Crispy and hot!',
      'color': '#D32F2F',
      'category': 'Chicken Tenders',
    },
    {
      'title': 'Family Feast',
      'subtitle': 'Enough for everyone',
      'color': '#C2185B',
      'category': 'Family Meals',
    },
    {
      'title': 'Cool Drinks',
      'subtitle': 'Quench your thirst',
      'color': '#512DA8',
      'category': 'Drinks',
    },
    {
      'title': 'Sweet Treats',
      'subtitle': 'End on a high note',
      'color': '#7B1FA2',
      'category': 'Desserts',
    },
    {
      'title': 'Combo Deals',
      'subtitle': 'Great value!',
      'color': '#0288D1',
      'category': 'Combos',
    },
  ];
  // 🚀 Performance: Optimized carousel item with cached color parsing
  Widget _buildCarouselItem(Map<String, String> item) {
    // Cache color parsing to avoid repeated computation
    final color = Color(int.parse(item['color']!.replaceAll('#', '0xFF')));

    return Container(
      decoration: BoxDecoration(
        color: color,
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            color,
            color.withValues(alpha: 0.8), // Use withValues for better performance
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Stack(
        children: [
          // 🚀 Performance: Optimized background image with caching
          Positioned.fill(
            child: Image.asset(
              'assets/CC-Penta-3.png',
              color: Colors.white.withValues(alpha: 0.1),
              colorBlendMode: BlendMode.srcOver,
              fit: BoxFit.contain,
              cacheWidth: 200, // Cache at reasonable size
              cacheHeight: 200,
              filterQuality: FilterQuality.low, // Lower quality for background
            ),
          ),
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  item['title']!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ).animate(onPlay: (controller) => controller.repeat())
                    .shimmer(duration: const Duration(seconds: 2)),
                const SizedBox(height: 8),
                Text(
                  item['subtitle']!,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                  ),
                  textAlign: TextAlign.center,
                ).animate()
                    .fadeIn(delay: const Duration(milliseconds: 300))
                    .slideY(
                  begin: 0.2,
                  end: 0,
                  curve: GSAPCurves.backOut,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LanguageService>(
      builder: (context, languageService, child) {
        return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: Stack(
        children: [
          // Main scrollable content
          RefreshIndicator(
            onRefresh: () async {
              final scaffoldMessenger = ScaffoldMessenger.of(context);

              await Future.delayed(const Duration(milliseconds: 500));
              if (mounted) {
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('Content refreshed!'),
                    duration: Duration(seconds: 1),
                  ),
                );
              }
            },
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Add top padding to account for sticky header
                  const SizedBox(height: 120),

                  // Carousel Header
                  CustomCarousel(
                    height: 300,
                    items: carouselItems.map((item) => _buildCarouselItem(item)).toList(),
                  ).animate()
                      .fadeIn(duration: AnimationDurations.normal)
                      .slideX(
                    begin: -0.25,
                    end: 0,
                    duration: AnimationDurations.normal,
                    curve: GSAPCurves.power2InOut,
                  ),

              // TASTE CHICA'S NASHVILLE HEAT! Section
const SizedBox(height: 50.0),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                padding: const EdgeInsets.fromLTRB(24, 50, 24, 20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: Theme.of(context).brightness == Brightness.dark
                        ? [
                            const Color(0xFF1a3d3d).withValues(alpha: 0.9),
                            const Color(0xFF0d2626).withValues(alpha: 0.8),
                          ]
                        : [
                            Colors.white.withValues(alpha: 0.8),
                            Colors.white.withValues(alpha: 0.5),
                          ],
                  ),
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: Colors.grey.withValues(alpha: 0.0),
                    width: 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // Main heading
                    Text(
                      languageService.getTranslatedText('TASTE CHICA\'S'),
                      style: const TextStyle(
                        fontSize: 50,
                        fontWeight: FontWeight.w900,
                        fontFamily: 'SofiaRoughBlackThree',
                        color: AppColors.spiceRed,
                        letterSpacing: 0.5,
                        height: 0.5, // Reduced line height to decrease spacing
                      ),
                      textAlign: TextAlign.center,
                    ).animate()
                        .fadeIn()
                        .slideY(
                      begin: -0.3,
                      end: 0,
                      duration: AnimationDurations.normal,
                      curve: GSAPCurves.power2InOut,
                    ),
                    Text(
                      languageService.getTranslatedText('NASHVILLE HEAT!'),
                      style: const TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.w900,
                        fontFamily: 'SofiaRoughBlackThree',
                        color: Color.fromARGB(255, 122, 59, 0),
                        letterSpacing: 1.0,
                      ),
                      textAlign: TextAlign.center,
                    ).animate()
                        .fadeIn(delay: const Duration(milliseconds: 200))
                        .slideY(
                      begin: -0.3,
                      end: 0,
                      delay: const Duration(milliseconds: 200),
                      duration: AnimationDurations.normal,
                      curve: GSAPCurves.power2InOut,
                    ),
                    const SizedBox(height: 18),

                    // Description text
                    Text(
                      'Have you tasted authentic Nashville-style hot chicken? Chica\'s Chicken brings the heat with freshly prepared, mouth-watering dishes. Order now for pickup or delivery!',
                      style: TextStyle(
                        fontSize: 16,
                        fontFamily: 'SofiaSans',
                        color: const Color.fromARGB(255, 122, 59, 0).withValues(alpha: 0.95),
                        height: 1.0,
                      ),
                      textAlign: TextAlign.center,
                    ).animate()
                        .fadeIn(delay: const Duration(milliseconds: 400))
                        .slideY(
                      begin: 0.2,
                      end: 0,
                      delay: const Duration(milliseconds: 400),
                      duration: AnimationDurations.normal,
                      curve: GSAPCurves.power2InOut,
                    ),
                    const SizedBox(height: 20),

                    // Call to action text
                    const Text(
                      'WE CUT & FRY UP! SO YOU CAN EAT UP!',
                      style: TextStyle(
                        fontSize: 18,
                        fontFamily: 'MontserratBlack',
                        fontWeight: FontWeight.bold,
                        color: Color.fromRGBO(155, 28, 36, 1),
                        letterSpacing: 0.2,
                      ),
                      textAlign: TextAlign.center,
                    ).animate()
                        .fadeIn(delay: const Duration(milliseconds: 600))
                        .slideX(
                      begin: -0.3,
                      end: 0,
                      delay: const Duration(milliseconds: 600),
                      duration: AnimationDurations.normal,
                      curve: GSAPCurves.power2InOut,
                    ),
                    const SizedBox(height: 20),

                    // ORDER NOW Button
                    MouseRegion(
                      onEnter: (_) => setState(() => _isButtonHovered = true),
                      onExit: (_) => setState(() => _isButtonHovered = false),
                      child: SizedBox(
                        width: 450,
                        child: ElevatedButton(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const OrderTypeSelectionScreen(),
                              ),
                            );
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.chicaOrange,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            elevation: _isButtonHovered ? 12 : 4,
                            animationDuration: const Duration(milliseconds: 200),
                          ),
                          child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              'assets/images/CC-Whole Chicken_White.png',
                              width: 40,
                              height: 40,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              languageService.getTranslatedText('Order Now').toUpperCase(),
                              style: const TextStyle(
                                fontSize: 25,
                                fontWeight: FontWeight.w900, // Black 900
                                letterSpacing: 0.5,
                                color: Colors.white,
                                fontFamily: 'MontserratBlack',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    ).animate()
                        .fadeIn(delay: const Duration(milliseconds: 800))
                        .slideY(
                      begin: 0.3,
                      end: 0,
                      delay: const Duration(milliseconds: 800),
                      duration: AnimationDurations.normal,
                      curve: GSAPCurves.backOut,
                    ),
                    const SizedBox(height: 24.0),
                  ],
                ),
              ),



              // Deal With It Section
              SizedBox(
                width: MediaQuery
                    .of(context)
                    .size
                    .width,
                child: HotDealsSection(
                  cartService: widget.cartService,
                  icon: const AssetImage('assets/images/CC-Whole Chicken_Orange.png'),
                  dealNameColor: Colors.green,
                ),
              ),

              // Personalized Recommendations Section
              SizedBox(
                width: MediaQuery
                    .of(context)
                    .size
                    .width,
                child: PersonalizedRecommendationsSection(
                  cartService: widget.cartService,
                  icon: const AssetImage('assets/images/CC-Whole Chicken_Orange.png'),
                  dealNameColor: Colors.green,
                ),
              ),

              // Global Footer
              const GlobalFooter(),
                ],
              ),
            ),
          ),

          // Sticky Header
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
height: 80,
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor.withValues(alpha: 0.75),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Empty space for balance
                      const SizedBox(width: 48),

                      // Centered Logo
                      Expanded(
                        child: Center(
                          child: Image.asset(
                            'assets/images/logo-landscape-colour.png',
                            height: 60,
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),

                      // Menu button
                      IconButton(
                        icon: const CustomMenuIcon(),
                        onPressed: () => _scaffoldKey.currentState?.openEndDrawer(),
                        tooltip: 'Open menu',
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      endDrawer: const NavigationMenuDrawer(),
    );
      },
    );
  }
}

class BulletPoint extends StatelessWidget {
  final String text;
  final int index;

  const BulletPoint({
    Key? key,
    required this.text,
    required this.index,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '• ',
            style: TextStyle(
              fontSize: 16,
              color: Theme
                  .of(context)
                  .primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 16,
                color: Theme
                    .of(context)
                    .textTheme
                    .bodyMedium
                    ?.color,
              ),
            ),
          ),
        ],
      ),
    ).animate()
        .fadeIn(delay: Duration(milliseconds: index * 200))
        .slideX(
      begin: 0.2,
      end: 0,
      delay: Duration(milliseconds: index * 200),
      duration: AnimationDurations.normal,
      curve: GSAPCurves.backOut,
    );
  }
}

class CheatDayTip extends StatelessWidget {
  final String text;
  final int index;

  const CheatDayTip({
    Key? key,
    required this.text,
    required this.index,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6, right: 12),
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: const Color(0xFFFF6B35),
              borderRadius: BorderRadius.circular(3),
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: Colors.white.withValues(alpha: 0.9),
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    ).animate()
        .fadeIn(delay: Duration(milliseconds: 1000 + (index * 100)))
        .slideX(
      begin: 0.3,
      end: 0,
      delay: Duration(milliseconds: 1000 + (index * 100)),
      duration: AnimationDurations.normal,
      curve: GSAPCurves.power2InOut,
    );
  }
}
