import 'package:qsr_app/models/cart.dart';
import 'package:qsr_app/models/menu_item.dart';
import 'package:qsr_app/models/crew_pack_selection.dart';
import 'package:qsr_app/models/menu_extras.dart';
import 'package:qsr_app/models/combo_selection.dart';
import 'package:qsr_app/services/menu_service.dart';

class CartService {
  static final CartService _instance = CartService._internal();
  static final Cart _cart = Cart();
  final MenuService _menuService = MenuService();

  factory CartService() {
    return _instance;
  }

  CartService._internal();

  Cart get cart => _cart;

  void addToCart(
    MenuItem menuItem, {
    String? selectedSize,
    Map<String, List<MenuItem>>? customizations,
    Map<String, List<dynamic>>? dynamicCustomizations,
    CrewPackCustomization? crewPackCustomization,
    MenuItemExtras? extras,
    ComboMeal? comboMeal,
    String? specialInstructions, // New parameter
  }) {
    // Clone the menu item to preserve all customizations
    final clonedMenuItem = menuItem.clone();

    // For combo meals, always add as a new item
    if (comboMeal != null) {
      _cart.items.add(CartItem(
        menuItem: clonedMenuItem,
        quantity: 1,
        selectedSize: selectedSize,
        comboMeal: comboMeal,
        extras: extras, // Pass extras to CartItem
        specialInstructions: specialInstructions, // Pass special instructions to CartItem
      ));
    }
    // For crew packs with sandwich selections, always add as a new item
    else if (crewPackCustomization != null) {
      _cart.items.add(CartItem(
        menuItem: clonedMenuItem,
        quantity: 1,
        selectedSize: selectedSize,
        customizations: customizations,
        dynamicCustomizations: dynamicCustomizations,
        crewPackCustomization: crewPackCustomization,
        extras: extras,
        specialInstructions: specialInstructions, // Pass special instructions
      ));
    }
    // For crew packs or customizable items, always add as a new item
    else if (customizations != null || extras != null || specialInstructions != null) { // Check for specialInstructions
      _cart.items.add(CartItem(
        menuItem: clonedMenuItem,
        quantity: 1,
        selectedSize: selectedSize,
        customizations: customizations,
        dynamicCustomizations: dynamicCustomizations,
        extras: extras,
        specialInstructions: specialInstructions, // Pass special instructions
      ));
    } else {
      // For regular items, check if they have customizations (like selected sauces) or extras or special instructions
      // If they do, always add as a new item to preserve the customizations
      if (clonedMenuItem.selectedSauces?.isNotEmpty == true ||
          clonedMenuItem.selectedBunType != null ||
          selectedSize != null ||
          extras != null ||
          specialInstructions != null) { // Check for specialInstructions
        // Item has customizations or extras, add as new item
        _cart.items.add(CartItem(
          menuItem: clonedMenuItem,
          quantity: 1,
          selectedSize: selectedSize,
          extras: extras,
          specialInstructions: specialInstructions, // Pass special instructions
        ));
      } else {
        // Check if identical item exists and update quantity
        final existingItem = _cart.items.firstWhere(
          (item) =>
              item.menuItem.id == menuItem.id &&
              item.customizations == null &&
              item.selectedSize == selectedSize &&
              item.crewPackCustomization == null &&
              (item.menuItem.selectedSauces?.isEmpty ?? true) &&
              item.menuItem.selectedBunType == null &&
              item.extras == null && // Check for extras
              item.specialInstructions == null, // Check for special instructions
          orElse: () => CartItem(
            menuItem: MenuItem(
              id: 'placeholder',
              name: '',
              description: '',
              price: 0,
              imageUrl: 'assets/placeholder.png',
              category: '',
            ),
            quantity: 0,
          ),
        );

        if (existingItem.menuItem.name.isNotEmpty) {
          existingItem.quantity++;
        } else {
          _cart.items.add(CartItem(
            menuItem: clonedMenuItem,
            quantity: 1,
            selectedSize: selectedSize,
            extras: extras,
            specialInstructions: specialInstructions, // Pass special instructions
          ));
        }
      }
    }
  }

  void removeItem(MenuItem menuItem, {
    Map<String, List<MenuItem>>? customizations,
    CrewPackCustomization? crewPackCustomization,
  }) {
    if (crewPackCustomization != null) {
      // For crew packs with sandwich selections, remove the specific customized version
      _cart.items.removeWhere((item) =>
          item.menuItem.id == menuItem.id &&
          item.crewPackCustomization == crewPackCustomization);
    } else if (customizations != null) {
      // For customized items, remove the specific customized version
      _cart.items.removeWhere((item) =>
          item.menuItem.id == menuItem.id &&
          item.customizations == customizations);
    } else {
      // For regular items, remove all non-customized versions
      _cart.items.removeWhere((item) =>
          item.menuItem.id == menuItem.id &&
          item.customizations == null &&
          item.crewPackCustomization == null);
    }
  }

  void updateQuantity(
    MenuItem menuItem,
    int quantity, {
    Map<String, List<MenuItem>>? customizations,
    CrewPackCustomization? crewPackCustomization,
  }) {
    final existingItem = _cart.items.firstWhere(
      (item) =>
          item.menuItem.id == menuItem.id &&
          item.customizations == customizations &&
          item.crewPackCustomization == crewPackCustomization,
      orElse: () => CartItem(
        menuItem: MenuItem(
          id: 'placeholder',
          name: '',
          description: '',
          price: 0,
          imageUrl: 'assets/placeholder.png',
          category: '',
        ),
        quantity: 0,
      ),
    );

    if (existingItem.menuItem.name.isNotEmpty) {
      existingItem.quantity = quantity;
    }
  }

  double getTotalPrice() {
    return _cart.totalPrice;
  }

  // Remove a specific cart item by index
  void removeCartItemByIndex(int index) {
    if (index >= 0 && index < _cart.items.length) {
      _cart.items.removeAt(index);
    }
  }

  // Update quantity of a specific cart item by index
  void updateCartItemQuantity(int index, int newQuantity) {
    if (index >= 0 && index < _cart.items.length) {
      if (newQuantity <= 0) {
        removeCartItemByIndex(index);
      } else {
        _cart.items[index].quantity = newQuantity;
      }
    }
  }

  // Update a cart item's customizations
  void updateCartItem(int index, {
    String? selectedSize,
    Map<String, List<MenuItem>>? customizations,
    CrewPackCustomization? crewPackCustomization,
    String? specialInstructions, // New parameter
  }) {
    if (index >= 0 && index < _cart.items.length) {
      final item = _cart.items[index];
      if (selectedSize != null) {
        item.selectedSize = selectedSize;
      }
      if (customizations != null) {
        item.customizations = customizations;
      }
      if (crewPackCustomization != null) {
        item.crewPackCustomization = crewPackCustomization;
      }
      if (specialInstructions != null) {
        item.specialInstructions = specialInstructions;
      }
    }
  }

  // Clear all items from cart
  void clearCart() {
    _cart.items.clear();
  }

  // Get cart item count
  int get itemCount => _cart.items.length;

  // Check if cart is empty
  bool get isEmpty => _cart.items.isEmpty;

  // Get cart items (missing method)
  List<CartItem> getCartItems() {
    return _cart.items;
  }

  /// Add a combo meal to cart
  void addComboToCart(
    ComboMeal combo, {
    MenuItemExtras? extras,
    String? specialInstructions,
  }) {
    addToCart(
      combo.mainItem,
      comboMeal: combo,
      extras: extras,
      specialInstructions: specialInstructions,
    );
  }

  /// Check if an item is eligible for combo upgrade
  bool isComboEligible(MenuItem item) {
    return ComboConfiguration.isComboEligible(item);
  }

  /// Create a combo from a menu item
  ComboMeal createCombo(MenuItem item, {String? selectedSize}) {
    return ComboConfiguration.createCombo(item, selectedSize: selectedSize);
  }

  /// ✅ Update heat level for a cart item
  void updateHeatLevel(CartItem cartItem, String newHeatLevel) {
    // Find the cart item in the list
    final itemIndex = _cart.items.indexOf(cartItem);
    if (itemIndex != -1) {
      if (cartItem.comboMeal != null) {
        // Update heat level for combo meal main item
        cartItem.comboMeal!.mainItem.selectedHeatLevel = newHeatLevel;
      } else {
        // Update heat level for regular item
        cartItem.menuItem.selectedHeatLevel = newHeatLevel;
      }
    }
  }

  /// ✅ Check if cart item has heat level selection capability
  bool canEditHeatLevel(CartItem cartItem) {
    if (cartItem.comboMeal != null) {
      return cartItem.comboMeal!.mainItem.allowsHeatLevelSelection;
    }
    return cartItem.menuItem.allowsHeatLevelSelection;
  }

  /// ✅ Get current heat level for cart item
  String? getCurrentHeatLevel(CartItem cartItem) {
    if (cartItem.comboMeal != null) {
      return cartItem.comboMeal!.mainItem.selectedHeatLevel;
    }
    return cartItem.menuItem.selectedHeatLevel;
  }

  /// 🍗 Get detailed crew pack selection information for display
  Future<List<CrewPackSelectionDetail>> getCrewPackSelectionDetails(CrewPackCustomization crewPackCustomization) async {
    List<CrewPackSelectionDetail> details = [];

    for (CrewPackSelection selection in crewPackCustomization.selections) {
      // Get the menu item details for this selection
      MenuItem? menuItem = await _menuService.getMenuItem(selection.sandwichId);

      if (menuItem != null) {
        details.add(CrewPackSelectionDetail(
          menuItem: menuItem,
          bunType: selection.bunType,
          heatLevel: selection.heatLevel,
          price: selection.price,
        ));
      } else {
        // Fallback if menu item not found
        details.add(CrewPackSelectionDetail(
          menuItem: MenuItem(
            id: selection.sandwichId,
            name: 'Unknown Item (${selection.sandwichId})',
            description: '',
            price: selection.price,
            imageUrl: 'assets/placeholder.png',
            category: 'Unknown',
          ),
          bunType: selection.bunType,
          heatLevel: selection.heatLevel,
          price: selection.price,
        ));
      }
    }

    return details;
  }

  /// 🍗 Get crew pack selection details synchronously using fallback data
  List<CrewPackSelectionDetail> getCrewPackSelectionDetailsSync(CrewPackCustomization crewPackCustomization) {
    List<CrewPackSelectionDetail> details = [];

    // Use fallback menu data for immediate display
    final fallbackMenuItems = _getFallbackMenuItems();

    for (CrewPackSelection selection in crewPackCustomization.selections) {
      // Find the menu item in fallback data
      MenuItem? menuItem = fallbackMenuItems.firstWhere(
        (item) => item.id == selection.sandwichId,
        orElse: () => MenuItem(
          id: selection.sandwichId,
          name: _getDisplayNameForId(selection.sandwichId),
          description: '',
          price: selection.price,
          imageUrl: 'assets/placeholder.png',
          category: 'Sandwiches',
        ),
      );

      details.add(CrewPackSelectionDetail(
        menuItem: menuItem,
        bunType: selection.bunType,
        heatLevel: selection.heatLevel,
        price: selection.price,
      ));
    }

    return details;
  }

  /// 📋 Get fallback menu items for immediate display
  List<MenuItem> _getFallbackMenuItems() {
    return [
      MenuItem(
        id: 'og_sando',
        name: 'The OG Sando',
        description: 'Nashville-spiced chicken breast',
        price: 13.00,
        imageUrl: 'assets/sandwiches.png',
        category: 'Sandwiches',
        allowsHeatLevelSelection: true,
      ),
      MenuItem(
        id: 'sweet_heat_sando',
        name: 'Sweet Heat Sando',
        description: 'Sweet heat sauce with pickled jalapeños',
        price: 13.00,
        imageUrl: 'assets/sandwiches.png',
        category: 'Sandwiches',
      ),
      MenuItem(
        id: 'buffalo_sando',
        name: 'Buffalo Sando',
        description: 'Classic buffalo sauce',
        price: 13.00,
        imageUrl: 'assets/sandwiches.png',
        category: 'Sandwiches',
      ),
      MenuItem(
        id: 'og_wings',
        name: 'OG Whole Wings',
        description: 'Nashville-spiced whole wings',
        price: 16.00,
        imageUrl: 'assets/whole_wings.png',
        category: 'Whole Wings',
        allowsHeatLevelSelection: true,
      ),
      MenuItem(
        id: 'chicken_bites',
        name: 'Chicken Bites',
        description: 'Bite-sized chicken pieces',
        price: 12.00,
        imageUrl: 'assets/chicken_bites.png',
        category: 'Chicken Bites',
      ),
    ];
  }

  /// 🏷️ Get display name for menu item ID
  String _getDisplayNameForId(String id) {
    switch (id) {
      case 'og_sando':
        return 'The OG Sando';
      case 'sweet_heat_sando':
        return 'Sweet Heat Sando';
      case 'buffalo_sando':
        return 'Buffalo Sando';
      case 'og_wings':
        return 'OG Whole Wings';
      case 'chicken_bites':
        return 'Chicken Bites';
      default:
        return 'Menu Item';
    }
  }
}

/// 🍗 Detailed crew pack selection for display purposes
class CrewPackSelectionDetail {
  final MenuItem menuItem;
  final String? bunType;
  final String? heatLevel;
  final double price;

  CrewPackSelectionDetail({
    required this.menuItem,
    this.bunType,
    this.heatLevel,
    required this.price,
  });

  /// Get formatted display text for this selection
  String get displayText {
    List<String> parts = [menuItem.name];

    if (bunType != null && bunType!.isNotEmpty) {
      parts.add('on $bunType');
    }

    if (heatLevel != null && heatLevel!.isNotEmpty && heatLevel != 'No Heat') {
      parts.add('($heatLevel heat)');
    }

    return parts.join(' ');
  }

  /// Get price difference text for display
  String get priceDisplayText {
    if (bunType == 'Brioche Bun') {
      return '+\$1.00';
    }
    return '';
  }
}
