{"accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "apps": {"shortcuts_arch": "", "shortcuts_version": 1}, "autocomplete": {"retention_policy_last_version": 138}, "autofill": {"last_version_deduped": 138}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1026, "left": 1300, "maximized": false, "right": 1876, "top": 26, "work_area_bottom": 1040, "work_area_left": 0, "work_area_right": 2560, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "1b32fe5e-f08e-4516-b69d-11100575fef5", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "138.0.7204.97"}, "gaia_cookie": {"changed_time": **********.994786, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "0cf3853f-b75c-495a-b301-14e76a1e02ca"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "PasswordManualFallbackAvailable": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "*****************", "recent_session_start_times": ["*****************", "*****************", "*****************", "*****************"], "session_last_active_time": "*****************", "session_start_time": "*****************"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "language_model_counters": {"en": 1}, "media": {"engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "8P0GLlOAX12tvo+u5r4YSg2cNzL0M8kaZ2VT73PaEzzrC0BNvuaKhZXM6do069NihpIjy9I0tSW1L+ibLOtNGQ=="}, "migrated_user_scripts_toggle": true, "ntp": {"num_personal_suggestions": 2}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "13396988574272415", "last_fetch_success": "13396988574379125"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "DIGITAL_CREDENTIALS_LOW_FRICTION": true, "EWALLET_MERCHANT_ALLOWLIST": true, "GLIC_ACTION_PAGE_BLOCK": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_INSIGHTS": true, "PRICE_TRACKING": true, "SALIENT_IMAGE": true, "SHOPPING_DISCOUNTS": true, "SHOPPING_PAGE_TYPES": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {"https://www.instagram.com:443,*": {"last_modified": "*****************", "setting": {"https://www.instagram.com/": {"next_install_text_animation": {"delay": "86400000000", "last_shown": "13396936487215726"}}, "https://www.instagram.com/?utm_source=pwa_homescreen&__pwa=1": {"couldShowBannerEvents": 1.3396936487215924e+16}}}}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://www.instagram.com:443,*": {"last_modified": "13396936486455273", "setting": {"client_hints": [1, 3, 11, 14, 15, 23]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"http://localhost,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]instagram.com,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]michelin.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "initialized_translations": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"http://localhost:50077,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:50905,*": {"expiration": "13404720284625011", "last_modified": "13396944284625016", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:51204,*": {"expiration": "13404615987023591", "last_modified": "13396839987023599", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:51451,*": {"expiration": "13404702271254322", "last_modified": "13396926271254328", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:53217,*": {"expiration": "13404716431023180", "last_modified": "13396940431023190", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:55431,*": {"expiration": "13404725101978249", "last_modified": "13396949101978259", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:55619,*": {"expiration": "13404616382287536", "last_modified": "13396840382287541", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:56880,*": {"expiration": "13404632487115384", "last_modified": "13396856487115390", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:57413,*": {"expiration": "13404555617805315", "last_modified": "13396779617805323", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:58886,*": {"expiration": "13404764382740321", "last_modified": "13396988382740330", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:62258,*": {"expiration": "13404778344205107", "last_modified": "13397002344205117", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:64502,*": {"expiration": "13404609599434224", "last_modified": "13396833599434229", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:65139,*": {"expiration": "13404719145880159", "last_modified": "13396943145880170", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "http://localhost:65322,*": {"expiration": "13404641160293448", "last_modified": "13396865160293455", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://guide.michelin.com:443,*": {"expiration": "13404712484608720", "last_modified": "13396936484608737", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://www.instagram.com:443,*": {"expiration": "13404712496544448", "last_modified": "13396936496544452", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "ondevice_languages_downloaded": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"http://localhost:50077,*": {"last_modified": "13396984995033150", "setting": {"lastEngagementTime": 1.3396871701103324e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 13.199999999999996}}, "http://localhost:50905,*": {"last_modified": "13396984995033110", "setting": {"lastEngagementTime": 1.339695137153658e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 4.8}}, "http://localhost:51204,*": {"last_modified": "13396984995033073", "setting": {"lastEngagementTime": 1.3396877700706492e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 15.0}}, "http://localhost:51451,*": {"last_modified": "13396984995033035", "setting": {"lastEngagementTime": 1.3396931822632412e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 7.799999999999998, "rawScore": 7.799999999999998}}, "http://localhost:53217,*": {"last_modified": "13396984995032994", "setting": {"lastEngagementTime": 1.3396943974517752e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}, "http://localhost:55431,*": {"last_modified": "13396984995032949", "setting": {"lastEngagementTime": 1.3396956195031196e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.3999999999999995}}, "http://localhost:55619,*": {"last_modified": "13396984995032876", "setting": {"lastEngagementTime": 1.3396878089144152e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.3999999999999995}}, "http://localhost:56880,*": {"last_modified": "13396984995032782", "setting": {"lastEngagementTime": 1.3396894199168304e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 11.999999999999996}}, "http://localhost:57413,*": {"last_modified": "13396984995032737", "setting": {"lastEngagementTime": 1.3396840456438628e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 8.399999999999999, "rawScore": 8.399999999999999}}, "http://localhost:58886,*": {"last_modified": "13396987988274499", "setting": {"lastEngagementTime": 1.3396987988274452e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}, "http://localhost:62258,*": {"last_modified": "13396995254915057", "setting": {"lastEngagementTime": 1.339699525491504e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}, "http://localhost:64502,*": {"last_modified": "13396984995032670", "setting": {"lastEngagementTime": 1.3396869540864932e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 0.0, "rawScore": 5.999999999999999}}, "http://localhost:65139,*": {"last_modified": "13396984995032508", "setting": {"lastEngagementTime": 1.3396949282777412e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 11.999999999999996, "rawScore": 11.999999999999996}}, "http://localhost:65322,*": {"last_modified": "13396984995031546", "setting": {"lastEngagementTime": 1.3396902854337036e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 8.399999999999999, "rawScore": 8.399999999999999}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "suspicious_notification_ids": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "138.0.7204.97", "creation_time": "13396779063885639", "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "13396995254915041", "last_time_obsolete_http_credentials_removed": 1752305523.930317, "last_time_password_store_metrics_reported": 1752450989.858604, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chrome", "password_hash_data_list": [], "safety_hub_menu_notifications": {"extensions": {"isCurrentlyActive": false, "result": {"timestamp": "13396986812212837", "triggeringExtensions": []}}, "notification-permissions": {"isCurrentlyActive": false, "result": {"notificationPermissions": [], "timestamp": "13396985022688522"}}, "passwords": {"isCurrentlyActive": false, "result": {"passwordCheckOrigins": [], "timestamp": "13396984996441056"}}, "safe-browsing": {"isCurrentlyActive": false, "onlyShowAfterTime": "13397073212212775", "result": {"safeBrowsingStatus": 1, "timestamp": "13396986812212795"}}, "unused-site-permissions": {"isCurrentlyActive": false, "result": {"permissions": [], "timestamp": "13396985022697659"}}}, "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13397244196773149", "hash_real_time_ohttp_key": "2AAgETtEl5cjzDpksr9J2MiGvUsUaS87sxbZ6vEn9Mxqv18ABAABAAI=", "metrics_last_log_time": "13396924559", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "CuUCChFjcm9zc19kZXZpY2VfdXNlchLPAgrDAg0AAIA/EI6x0I7pjeYXGrACCqcCGqQCChkNAACAPxISTm9Dcm9zc0RldmljZVVzYWdlChgNAAAAQBIRQ3Jvc3NEZXZpY2VNb2JpbGUKGQ0AAEBAEhJDcm9zc0RldmljZURlc2t0b3AKGA0AAIBAEhFDcm9zc0RldmljZVRhYmxldAoiDQAAoEASG0Nyb3NzRGV2aWNlTW9iaWxlQW5kRGVza3RvcAohDQAAwEASGkNyb3NzRGV2aWNlTW9iaWxlQW5kVGFibGV0CiINAADgQBIbQ3Jvc3NEZXZpY2VEZXNrdG9wQW5kVGFibGV0CiANAAAAQRIZQ3Jvc3NEZXZpY2VBbGxEZXZpY2VUeXBlcwoXDQAAEEESEENyb3NzRGV2aWNlT3RoZXISEk5vQ3Jvc3NEZXZpY2VVc2FnZRIEEAcYBCACEI+y0I7pjeYXClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQ8qzQjumN5hcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEIeKyqDKj+YX", "device_switcher_util": {"result": {"labels": ["NotSynced"]}}, "last_db_compaction_time": "13396838399000000", "uma_in_sql_start_time": "13396779063935286"}, "sessions": {"event_log": [{"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396840382275369", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396854603354583", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396856487103348", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396864859844314", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396865160283035", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396924559860860", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396926271246905", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396926428415835", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396940431011250", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396941190039202", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396943145869957", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396943879126114", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396944284613283", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396948226100948", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396949101966731", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396984994806555", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13396988382729963", "type": 2, "window_count": 1}, {"crashed": false, "time": "13396988564273769", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13397002344195289", "type": 2, "window_count": 1}], "session_data_status": 3}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "138"}}