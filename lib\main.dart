// This is the main starting point of our app!
import 'package:qsr_app/screens/menu_screen.dart';
import 'package:flutter/material.dart';

import 'package:provider/provider.dart';
// NO FIREBASE IMPORTS - Using local notifications only!
import 'screens/loading_screen.dart';
import 'layouts/main_layout.dart';
import 'screens/terms_of_use_screen.dart';
import 'screens/privacy_policy_screen.dart';
import 'screens/login_screen.dart';
import 'screens/signup_screen.dart';
import 'screens/password_reset_screen.dart';
import 'screens/loyalty_screen.dart'; // NEW: Import loyalty screen for deep linking
import 'screens/notification_settings_screen.dart'; // NEW: Import notification settings
import 'screens/notification_test_screen.dart'; // NEW: Import notification test screen
// Import MenuItemScreen
import 'services/cart_service.dart'; // Import CartService
import 'services/navigation_service.dart';
import 'services/theme_service.dart' as theme_service;
import 'services/language_service.dart';
import 'services/user_preferences_service.dart';
import 'services/data_sync_service.dart';
import 'services/performance_service.dart';
import 'services/mock_auth_service.dart';
import 'services/notification_service.dart' as ns; // NEW: Import notification service
import 'services/notification_service_local_only.dart';
import 'themes/app_theme.dart';
import 'widgets/offline_indicator.dart';



// This is the main function that runs when the app starts.
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // NO FIREBASE! Using local notifications only to avoid web compilation errors

  // 🚀 Performance: Initialize services asynchronously for faster startup
  final themeService = theme_service.ThemeService();
  final languageService = LanguageService();
  final userPreferencesService = UserPreferencesService();
  final dataSyncService = DataSyncService();
  final performanceService = PerformanceService();
  final authService = MockAuthService();
    final ns.NotificationService notificationService = LocalOnlyNotificationService() as ns.NotificationService; // NEW: Notification service

  // Initialize critical services first (theme for immediate UI)
  await themeService.initialize();
  await languageService.initialize();

  // Initialize other services in parallel for faster startup
  await Future.wait([
    userPreferencesService.initialize(),
    dataSyncService.initialize(),
    performanceService.initialize(),
    authService.initialize(),
    notificationService.initialize(), // NEW: Initialize notifications
  ]);

  runApp(MyApp(
    themeService: themeService,
    languageService: languageService,
    authService: authService,
    notificationService: notificationService, // NEW: Pass notification service
  )); // We tell Flutter to run our main app widget, MyApp.
}

// MyApp is like the main container for our whole app.
// It sets up the basic look and feel using Material Design.
class MyApp extends StatefulWidget {
  final theme_service.ThemeService themeService;
  final LanguageService languageService;
  final MockAuthService authService;
  final ns.NotificationService notificationService; // NEW: Notification service

  const MyApp({
    super.key,
    required this.themeService,
    required this.languageService,
    required this.authService,
    required this.notificationService, // NEW: Required notification service
  });

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late final ns.NotificationService notificationService;

  @override
  void initState() {
    super.initState();
    notificationService = widget.notificationService;

    // Listen for notification taps and handle deep linking
    notificationService.notificationStream.listen((notification) {
      _handleDeepLink(notification['route']);
    });
  }

  // Handle deep linking when notification is tapped
  void _handleDeepLink(String route) {
    // Navigate to the specified route
    if (NavigationService.navigatorKey.currentState != null) {
      NavigationService.navigatorKey.currentState!.pushNamed(route);
    }
  }

  @override
  Widget build(BuildContext context) {
    // MaterialApp is like the main building block for a Material Design app.
    // It sets up things like the app's title, theme, and the first screen to show.
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: widget.authService),
        ChangeNotifierProvider.value(value: widget.themeService),
        ChangeNotifierProvider.value(value: widget.languageService),
        Provider<ns.NotificationService>.value(value: widget.notificationService),
      ],
      child: ListenableBuilder(
        listenable: widget.languageService,
        builder: (context, child) {
          return ListenableBuilder(
            listenable: widget.themeService,
            builder: (context, child) {
              return PerformanceMonitor(
            child: OfflineIndicator(
              child: MaterialApp(
                title: "Chica's Chicken", // The name of our app
                debugShowCheckedModeBanner: false,
                navigatorKey: NavigationService.navigatorKey,
                theme: AppTheme.lightTheme,
                darkTheme: AppTheme.darkTheme,
                themeMode: _getThemeMode(),
                initialRoute: '/',
                routes: {
                  '/': (context) => const LoadingScreen(),
                  '/home': (context) => const MainLayout(),
                  '/games': (context) => const MainLayout(initialPage: 1),
                  '/menu': (context) => const MainLayout(initialPage: 2),
                  '/cart': (context) => const MainLayout(initialPage: 3),
                  '/loyalty': (context) => const MainLayout(initialPage: 4),
                  '/login': (context) => const LoginScreen(),
                  '/signup': (context) => const SignupScreen(),
                  '/password-reset': (context) => const PasswordResetScreen(),
                  '/terms-of-use': (context) => const TermsOfUseScreen(),
                  '/privacy-policy': (context) => const PrivacyPolicyScreen(),
                  '/loyalty': (context) => const LoyaltyScreen(), // NEW: Deep link route
                  '/notification-settings': (context) => const NotificationSettingsScreen(), // NEW: Settings route
                  '/notification-test': (context) => const NotificationTestScreen(), // NEW: Test route
                  '/menu': (context) => MenuScreen(cartService: CartService()),
                  // 🚀 Developer routes removed for production
                },
              ),
            ),
          );
            },
          );
        },
      ),
    );
  }

  ThemeMode _getThemeMode() {
    switch (widget.themeService.themeMode) {
      case theme_service.ThemeMode.light:
        return ThemeMode.light;
      case theme_service.ThemeMode.dark:
        return ThemeMode.dark;
      case theme_service.ThemeMode.system:
        return ThemeMode.system;
    }
  }
}

// NOTE: Suggested folder structure for future features:
// lib/
//   main.dart (This file)
//   models/ (For data structures like Menu, Item, Cart)
//   services/ (For API calls and other background tasks, like Revel API integration)
//   widgets/ (For reusable UI pieces)
//   screens/ (For different screens like MenuScreen, CartScreen, CheckoutScreen)
//   utils/ (For helper functions)

// NOTE: If you need to add Firebase later for notifications or other features,
// you would typically add the necessary Firebase dependencies to your pubspec.yaml file
// and then follow the Firebase setup instructions for Flutter.
// You might add Firebase initialization code in your main() function
// and create new files/folders under lib/ for Firebase-related logic.
