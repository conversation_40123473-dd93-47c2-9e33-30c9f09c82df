import 'package:flutter/material.dart';
import '../models/menu_item.dart';
import '../services/recommendation_service.dart';
import '../services/cart_service.dart';
import '../widgets/deal_card.dart';
import '../screens/order_type_selection_screen.dart';

class PersonalizedRecommendationsSection extends StatefulWidget {
  final CartService cartService;
  final ImageProvider? icon;
  final Color? dealNameColor;

  const PersonalizedRecommendationsSection({
    Key? key,
    required this.cartService,
    this.icon,
    this.dealNameColor,
  }) : super(key: key);

  @override
  State<PersonalizedRecommendationsSection> createState() =>
      _PersonalizedRecommendationsSectionState();
}

class _PersonalizedRecommendationsSectionState
    extends State<PersonalizedRecommendationsSection> {
  final RecommendationService _recommendationService = RecommendationService();
  List<MenuItem> _recommendations = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadRecommendations();
  }

  Future<void> _loadRecommendations() async {
    try {
      final recommendations =
          await _recommendationService.getPersonalizedRecommendations();
      setState(() {
        _recommendations = recommendations;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      // Handle error
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title Section with Gradient
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 30),
          padding: const EdgeInsets.fromLTRB(24, 32, 24, 24),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: Colors.grey.withValues(alpha: 0.0),
              width: 1,
            ),
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.white.withValues(alpha: 0.7),
                Colors.white.withValues(alpha: 0.9),
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  if (widget.icon != null)
                    Padding(
                      padding: const EdgeInsets.only(right: 6.0),
                      child: ImageIcon(
                        widget.icon!,
                        color: const Color.fromRGBO(255, 107, 53, 1),
                        size: 28,
                      ),
                    ),
                  Text(
                    'PAST PICKS',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w900,
                          fontFamily: 'MontserratBlack',
                          color: widget.dealNameColor ?? Colors.grey.withValues(alpha: 0.3),
                          fontSize: 25,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 0),
              const Text(
                'Based on your previous orders',
                style: TextStyle(
                  color: Color.fromRGBO(155, 28, 36, 1),
                  fontSize: 15,
                ),
              ),
            ],
          ),
        ),
        if (_isLoading)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32.0),
              child: CircularProgressIndicator(),
            ),
          )
        else if (_recommendations.isEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.all(32.0),
              child: Text(
                'Check back later for personalized recommendations!',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ),
          )
        else
          SizedBox(
            height: 450, // Increased height for the recommendation cards
            child: MouseRegion(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                scrollDirection: Axis.horizontal,
                itemCount: _recommendations.length,
                itemBuilder: (context, index) {
                  final recommendation = _recommendations[index];
                  return SizedBox(
                    width: 300, // Fixed width for each card
                    child: Padding(
                      padding: const EdgeInsets.only(right: 16),
                      child: DealCard(
                        deal: _recommendations[index],
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const OrderTypeSelectionScreen(),
                            ),
                          );
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
      ],
    );
  }
}
