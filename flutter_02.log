Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter build web

## exception

FileSystemException: FileSystemException: Failed to decode data using encoding 'utf-8', path = 'D:\Bizness\7thSenseMediaLabz\Chicas Chicken Flutter\.gitignore'

```
#0      _File._tryDecode (dart:io/file_impl.dart:707:7)
#1      _File.readAsStringSync (dart:io/file_impl.dart:718:7)
#2      _File.readAsLinesSync (dart:io/file_impl.dart:724:36)
#3      ForwardingFile.readAsLinesSync (package:file/src/forwarding/forwarding_file.dart:107:16)
#4      ForwardingFile.readAsLinesSync (package:file/src/forwarding/forwarding_file.dart:107:16)
#5      ProjectMigrator.processFileLines (package:flutter_tools/src/base/project_migrator.dart:40:37)
#6      ScrubGeneratedPluginRegistrant.migrate (package:flutter_tools/src/web/migrations/scrub_generated_plugin_registrant.dart:28:7)
#7      ProjectMigration.run (package:flutter_tools/src/base/project_migrator.dart:86:22)
#8      WebBuilder.buildWeb (package:flutter_tools/src/web/compile.dart:86:21)
<asynchronous suspension>
#9      BuildWebCommand.runCommand (package:flutter_tools/src/commands/build_web.dart:246:5)
<asynchronous suspension>
#10     FlutterCommand.run.<anonymous closure> (package:flutter_tools/src/runner/flutter_command.dart:1563:27)
<asynchronous suspension>
#11     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#12     CommandRunner.runCommand (package:args/command_runner.dart:212:13)
<asynchronous suspension>
#13     FlutterCommandRunner.runCommand.<anonymous closure> (package:flutter_tools/src/runner/flutter_command_runner.dart:494:9)
<asynchronous suspension>
#14     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#15     FlutterCommandRunner.runCommand (package:flutter_tools/src/runner/flutter_command_runner.dart:431:5)
<asynchronous suspension>
#16     run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:98:11)
<asynchronous suspension>
#17     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:154:19)
<asynchronous suspension>
#18     main (package:flutter_tools/executable.dart:102:3)
<asynchronous suspension>
```

## flutter doctor

```
[32m[✓][39m Flutter (Channel stable, 3.32.4, on Microsoft Windows [Version 10.0.19045.6036], locale en-US) [1,036ms]
    [32m•[39m Flutter version 3.32.4 on channel stable at C:\flutter
    [32m•[39m Upstream repository https://github.com/flutter/flutter.git
    [32m•[39m Framework revision 6fba2447e9 (2 weeks ago), 2025-06-12 19:03:56 -0700
    [32m•[39m Engine revision 8cd19e509d
    [32m•[39m Dart version 3.8.1
    [32m•[39m DevTools version 2.45.1

[32m[✓][39m Windows Version (Windows 10, 22H2, 2009) [3.9s]

[32m[✓][39m Android toolchain - develop for Android devices (Android SDK version 35.0.1) [3.7s]
    [32m•[39m Android SDK at C:\Users\<USER>\AppData\Local\Android\sdk
    [32m•[39m Platform android-35, build-tools 35.0.1
    [32m•[39m Java binary at: D:\PC Programs\Android Studio Meerkat_Stable\jbr\bin\java
      This is the JDK bundled with the latest Android Studio installation on this machine.
      To manually set the JDK path, use: `flutter config --jdk-dir="path/to/jdk"`.
    [32m•[39m Java version OpenJDK Runtime Environment (build 21.0.6+-13368085-b895.109)
    [32m•[39m All Android licenses accepted.

[32m[✓][39m Chrome - develop for the web [136ms]
    [32m•[39m Chrome at C:\Program Files\Google\Chrome\Application\chrome.exe

[32m[✓][39m Visual Studio - develop Windows apps (Visual Studio Community 2022 17.14.6 (June 2025)) [134ms]
    [32m•[39m Visual Studio at C:\Program Files\Microsoft Visual Studio\2022\Community
    [32m•[39m Visual Studio Community 2022 version 17.14.36212.18
    [32m•[39m Windows 10 SDK version 10.0.26100.0

[32m[✓][39m Android Studio (version 2024.3.2) [20ms]
    [32m•[39m Android Studio at D:\PC Programs\Android Studio Meerkat_Stable
    [32m•[39m Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    [32m•[39m Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    [32m•[39m Java version OpenJDK Runtime Environment (build 21.0.6+-13368085-b895.109)

[32m[✓][39m VS Code (version 1.101.0) [18ms]
    [32m•[39m VS Code at C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code
    [32m•[39m Flutter extension version 3.112.0

[32m[✓][39m VS Code (version 1.102.0-insider) [18ms]
    [32m•[39m VS Code at C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code Insiders
    [32m•[39m Flutter extension version 3.112.0

[32m[✓][39m Connected device (3 available) [220ms]
    [32m•[39m Windows (desktop) • windows • windows-x64    • Microsoft Windows [Version 10.0.19045.6036]
    [32m•[39m Chrome (web)      • chrome  • web-javascript • Google Chrome 137.0.7151.120
    [32m•[39m Edge (web)        • edge    • web-javascript • Microsoft Edge 138.0.3351.55

[32m[✓][39m Network resources [519ms]
    [32m•[39m All expected network resources are available.

[32m•[39m No issues found!
```
