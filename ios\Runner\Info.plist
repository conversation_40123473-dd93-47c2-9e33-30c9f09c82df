<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Chica's Chicken</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>qsr_app</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>

	<!-- Deep linking URL schemes -->
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>myqsrapp.deeplink</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>myqsrapp</string>
			</array>
		</dict>
	</array>

	<!-- Background modes for notifications -->
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>

	<!-- Camera and microphone permissions for Social Rewards feature -->
	<key>NSCameraUsageDescription</key>
	<string>Chica's Chicken needs camera access to let you take photos of your delicious meals for Social Rewards. Share your food photos on social media and earn 10 loyalty points each time!</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Chica's Chicken needs microphone access to record videos of your dining experience for Social Rewards. This helps you share your experience and earn loyalty points!</string>

	<!-- Photo library permissions for saving and sharing -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Chica's Chicken needs photo library access to save and share your food photos for Social Rewards. Your photos are shared directly to social media - we don't store them on our servers.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Chica's Chicken can save your food photos to your photo library so you can share them later and earn loyalty points through Social Rewards.</string>
</dict>
</plist>
